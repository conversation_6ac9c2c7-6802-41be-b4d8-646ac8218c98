import 'package:flutter/material.dart';
import 'package:stillpoint/screens/loading_screen.dart';
import 'package:stillpoint/services/user_profile_service.dart';

class BuildSpaceScreen extends StatefulWidget {
  final String name;
  final int age;
  final String location;
  final String countryCode;

  const BuildSpaceScreen({
    super.key,
    required this.name,
    required this.age,
    required this.location,
    required this.countryCode,
  });

  @override
  State<BuildSpaceScreen> createState() => _BuildSpaceScreenState();
}

class _BuildSpaceScreenState extends State<BuildSpaceScreen>
    with TickerProviderStateMixin {
  final List<Map<String, dynamic>> _tags = [
    {
      'name': 'Anxiety',
      'icon': Icons.psychology_outlined,
      'color': const Color(0xFF6366F1),
    },
    {
      'name': 'Childhood Trauma',
      'icon': Icons.child_care_outlined,
      'color': const Color(0xFF8B5CF6),
    },
    {
      'name': 'Relationships',
      'icon': Icons.favorite_outline,
      'color': const Color(0xFF3B82F6),
    },
    {
      'name': 'Stress',
      'icon': Icons.trending_down_outlined,
      'color': const Color(0xFF6366F1),
    },
    {
      'name': 'Work',
      'icon': Icons.work_outline,
      'color': const Color(0xFF3B82F6),
    },
    {
      'name': 'Family',
      'icon': Icons.family_restroom_outlined,
      'color': const Color(0xFF8B5CF6),
    },
    {
      'name': 'Loss',
      'icon': Icons.sentiment_dissatisfied_outlined,
      'color': const Color(0xFF6366F1),
    },
    {
      'name': 'Self Esteem',
      'icon': Icons.self_improvement_outlined,
      'color': const Color(0xFF3B82F6),
    },
    {
      'name': 'Mindfulness',
      'icon': Icons.spa_outlined,
      'color': const Color(0xFF8B5CF6),
    },
    {
      'name': 'Motivation',
      'icon': Icons.rocket_launch_outlined,
      'color': const Color(0xFF6366F1),
    },
    {
      'name': 'Confidence',
      'icon': Icons.emoji_emotions_outlined,
      'color': const Color(0xFF3B82F6),
    },
    {
      'name': 'LGBTQ+',
      'icon': Icons.diversity_1_outlined,
      'color': const Color(0xFF8B5CF6),
    },
    {
      'name': 'Loneliness',
      'icon': Icons.person_outline,
      'color': const Color(0xFF6366F1),
    },
  ];

  final Set<String> _selectedTags = {};
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(title: const Text('Build Your Space'), elevation: 0),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              colorScheme.tertiary.withValues(alpha: 0.05),
              colorScheme.surface,
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        const SizedBox(height: 32),

                        // Header section
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: colorScheme.surface,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.05),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              Icon(
                                Icons.psychology_outlined,
                                size: 48,
                                color: colorScheme.tertiary,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'Build Your Space',
                                style: theme.textTheme.headlineMedium?.copyWith(
                                  color: colorScheme.tertiary,
                                  fontWeight: FontWeight.w600,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Select the areas you\'d like to focus on. You can always change these later in your journey.',
                                style: theme.textTheme.bodyLarge?.copyWith(
                                  color: colorScheme.onSurfaceVariant,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Tags section in a card
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.all(24),
                            decoration: BoxDecoration(
                              color: colorScheme.surface,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.05),
                                  blurRadius: 10,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Focus Areas',
                                  style: theme.textTheme.titleLarge?.copyWith(
                                    color: colorScheme.tertiary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Expanded(
                                  child: SingleChildScrollView(
                                    child: Wrap(
                                      spacing: 12,
                                      runSpacing: 12,
                                      children:
                                          _tags.map((tagData) {
                                            final tagName =
                                                tagData['name'] as String;
                                            final tagIcon =
                                                tagData['icon'] as IconData;
                                            final tagColor =
                                                tagData['color'] as Color;
                                            final isSelected = _selectedTags
                                                .contains(tagName);

                                            return AnimatedContainer(
                                              duration: const Duration(
                                                milliseconds: 200,
                                              ),
                                              child: InkWell(
                                                onTap: () {
                                                  setState(() {
                                                    if (isSelected) {
                                                      _selectedTags.remove(
                                                        tagName,
                                                      );
                                                    } else {
                                                      _selectedTags.add(
                                                        tagName,
                                                      );
                                                    }
                                                  });
                                                },
                                                borderRadius:
                                                    BorderRadius.circular(16),
                                                child: Container(
                                                  padding:
                                                      const EdgeInsets.symmetric(
                                                        horizontal: 16,
                                                        vertical: 12,
                                                      ),
                                                  decoration: BoxDecoration(
                                                    color:
                                                        isSelected
                                                            ? tagColor
                                                                .withValues(
                                                                  alpha: 0.15,
                                                                )
                                                            : colorScheme
                                                                .surfaceContainerHighest
                                                                .withValues(
                                                                  alpha: 0.3,
                                                                ),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          16,
                                                        ),
                                                    border: Border.all(
                                                      color:
                                                          isSelected
                                                              ? tagColor
                                                              : colorScheme
                                                                  .outline
                                                                  .withValues(
                                                                    alpha: 0.3,
                                                                  ),
                                                      width: isSelected ? 2 : 1,
                                                    ),
                                                    boxShadow:
                                                        isSelected
                                                            ? [
                                                              BoxShadow(
                                                                color: tagColor
                                                                    .withValues(
                                                                      alpha:
                                                                          0.2,
                                                                    ),
                                                                blurRadius: 8,
                                                                offset:
                                                                    const Offset(
                                                                      0,
                                                                      2,
                                                                    ),
                                                              ),
                                                            ]
                                                            : [
                                                              BoxShadow(
                                                                color: Colors
                                                                    .black
                                                                    .withValues(
                                                                      alpha:
                                                                          0.05,
                                                                    ),
                                                                blurRadius: 4,
                                                                offset:
                                                                    const Offset(
                                                                      0,
                                                                      2,
                                                                    ),
                                                              ),
                                                            ],
                                                  ),
                                                  child: Row(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      Icon(
                                                        tagIcon,
                                                        size: 20,
                                                        color:
                                                            isSelected
                                                                ? tagColor
                                                                : colorScheme
                                                                    .onSurfaceVariant,
                                                      ),
                                                      const SizedBox(width: 8),
                                                      Text(
                                                        tagName,
                                                        style: theme
                                                            .textTheme
                                                            .bodyMedium
                                                            ?.copyWith(
                                                              color:
                                                                  isSelected
                                                                      ? tagColor
                                                                      : colorScheme
                                                                          .onSurface,
                                                              fontWeight:
                                                                  isSelected
                                                                      ? FontWeight
                                                                          .w600
                                                                      : FontWeight
                                                                          .w500,
                                                            ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            );
                                          }).toList(),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        // Progress indicator
                        if (_selectedTags.isNotEmpty) ...[
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: colorScheme.tertiary.withValues(
                                alpha: 0.1,
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.check_circle_outline,
                                  color: colorScheme.tertiary,
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  '${_selectedTags.length} area${_selectedTags.length == 1 ? '' : 's'} selected',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    color: colorScheme.tertiary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),
                        ],

                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ),
                // Continue button at bottom
                Container(
                  padding: const EdgeInsets.all(24.0),
                  child: ElevatedButton(
                    onPressed:
                        _selectedTags.isEmpty
                            ? null
                            : () async {
                              // Save user profile
                              await UserProfileService.saveProfile(
                                name: widget.name,
                                age: widget.age,
                                location: widget.location,
                                countryCode: widget.countryCode,
                                focusAreas: _selectedTags.toList(),
                              );

                              // Mark onboarding as complete
                              await UserProfileService.completeOnboarding();

                              if (mounted) {
                                Navigator.pushAndRemoveUntil(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) => LoadingScreen(
                                          userName: widget.name,
                                          selectedAreas: _selectedTags.toList(),
                                        ),
                                  ),
                                  (route) => false,
                                );
                              }
                            },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorScheme.tertiary,
                      foregroundColor: Colors.white,
                      disabledBackgroundColor: colorScheme.outline.withValues(
                        alpha: 0.3,
                      ),
                      elevation: _selectedTags.isEmpty ? 0 : 3,
                      shadowColor: colorScheme.tertiary.withValues(alpha: 0.3),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 18),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (_selectedTags.isNotEmpty) ...[
                          const Icon(Icons.rocket_launch_outlined, size: 20),
                          const SizedBox(width: 8),
                        ],
                        Text(
                          _selectedTags.isEmpty
                              ? 'Select at least one area to continue'
                              : 'Complete Onboarding',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
