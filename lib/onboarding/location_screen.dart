import 'package:flutter/material.dart';
import 'package:stillpoint/onboarding/build_space_screen.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

class LocationScreen extends StatefulWidget {
  final String name;
  final int age;

  const LocationScreen({super.key, required this.name, required this.age});

  @override
  State<LocationScreen> createState() => _LocationScreenState();
}

class _LocationScreenState extends State<LocationScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  String? _selectedCountry;
  String? _selectedCountryCode;
  bool _isDetectingLocation = true;
  bool _locationDetectionFailed = false;
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, String>> _filteredCountries = [];

  // Countries with full mental health support
  final Set<String> _supportedCountries = {
    'US',
    'CA',
    'GB',
    'AU',
    'FR',
    'DE',
    'IN',
    'PH',
  };

  final List<Map<String, String>> _countries = [
    {'name': 'Afghanistan', 'code': 'AF', 'flag': '🇦🇫'},
    {'name': 'Albania', 'code': 'AL', 'flag': '🇦🇱'},
    {'name': 'Algeria', 'code': 'DZ', 'flag': '🇩🇿'},
    {'name': 'Argentina', 'code': 'AR', 'flag': '🇦🇷'},
    {'name': 'Armenia', 'code': 'AM', 'flag': '🇦🇲'},
    {'name': 'Australia', 'code': 'AU', 'flag': '🇦🇺'},
    {'name': 'Austria', 'code': 'AT', 'flag': '🇦🇹'},
    {'name': 'Azerbaijan', 'code': 'AZ', 'flag': '🇦🇿'},
    {'name': 'Bahrain', 'code': 'BH', 'flag': '🇧🇭'},
    {'name': 'Bangladesh', 'code': 'BD', 'flag': '🇧🇩'},
    {'name': 'Belarus', 'code': 'BY', 'flag': '🇧🇾'},
    {'name': 'Belgium', 'code': 'BE', 'flag': '🇧🇪'},
    {'name': 'Bolivia', 'code': 'BO', 'flag': '🇧🇴'},
    {'name': 'Bosnia and Herzegovina', 'code': 'BA', 'flag': '🇧🇦'},
    {'name': 'Brazil', 'code': 'BR', 'flag': '🇧🇷'},
    {'name': 'Bulgaria', 'code': 'BG', 'flag': '🇧🇬'},
    {'name': 'Cambodia', 'code': 'KH', 'flag': '🇰🇭'},
    {'name': 'Canada', 'code': 'CA', 'flag': '🇨🇦'},
    {'name': 'Chile', 'code': 'CL', 'flag': '🇨🇱'},
    {'name': 'China', 'code': 'CN', 'flag': '🇨🇳'},
    {'name': 'Colombia', 'code': 'CO', 'flag': '🇨🇴'},
    {'name': 'Croatia', 'code': 'HR', 'flag': '🇭🇷'},
    {'name': 'Czech Republic', 'code': 'CZ', 'flag': '🇨🇿'},
    {'name': 'Denmark', 'code': 'DK', 'flag': '🇩🇰'},
    {'name': 'Ecuador', 'code': 'EC', 'flag': '🇪🇨'},
    {'name': 'Egypt', 'code': 'EG', 'flag': '🇪🇬'},
    {'name': 'Estonia', 'code': 'EE', 'flag': '🇪🇪'},
    {'name': 'Ethiopia', 'code': 'ET', 'flag': '🇪🇹'},
    {'name': 'Finland', 'code': 'FI', 'flag': '🇫🇮'},
    {'name': 'France', 'code': 'FR', 'flag': '🇫🇷'},
    {'name': 'Georgia', 'code': 'GE', 'flag': '🇬🇪'},
    {'name': 'Germany', 'code': 'DE', 'flag': '🇩🇪'},
    {'name': 'Ghana', 'code': 'GH', 'flag': '🇬🇭'},
    {'name': 'Greece', 'code': 'GR', 'flag': '🇬🇷'},
    {'name': 'Hungary', 'code': 'HU', 'flag': '🇭🇺'},
    {'name': 'Iceland', 'code': 'IS', 'flag': '🇮🇸'},
    {'name': 'India', 'code': 'IN', 'flag': '🇮🇳'},
    {'name': 'Indonesia', 'code': 'ID', 'flag': '🇮🇩'},
    {'name': 'Iran', 'code': 'IR', 'flag': '🇮🇷'},
    {'name': 'Iraq', 'code': 'IQ', 'flag': '🇮🇶'},
    {'name': 'Ireland', 'code': 'IE', 'flag': '🇮🇪'},
    {'name': 'Israel', 'code': 'IL', 'flag': '🇮🇱'},
    {'name': 'Italy', 'code': 'IT', 'flag': '🇮🇹'},
    {'name': 'Japan', 'code': 'JP', 'flag': '🇯🇵'},
    {'name': 'Jordan', 'code': 'JO', 'flag': '🇯🇴'},
    {'name': 'Kazakhstan', 'code': 'KZ', 'flag': '🇰🇿'},
    {'name': 'Kenya', 'code': 'KE', 'flag': '🇰🇪'},
    {'name': 'Kuwait', 'code': 'KW', 'flag': '🇰🇼'},
    {'name': 'Latvia', 'code': 'LV', 'flag': '🇱🇻'},
    {'name': 'Lebanon', 'code': 'LB', 'flag': '🇱🇧'},
    {'name': 'Lithuania', 'code': 'LT', 'flag': '🇱🇹'},
    {'name': 'Luxembourg', 'code': 'LU', 'flag': '🇱🇺'},
    {'name': 'Malaysia', 'code': 'MY', 'flag': '🇲🇾'},
    {'name': 'Mexico', 'code': 'MX', 'flag': '🇲🇽'},
    {'name': 'Morocco', 'code': 'MA', 'flag': '🇲🇦'},
    {'name': 'Netherlands', 'code': 'NL', 'flag': '🇳🇱'},
    {'name': 'New Zealand', 'code': 'NZ', 'flag': '🇳🇿'},
    {'name': 'Nigeria', 'code': 'NG', 'flag': '🇳🇬'},
    {'name': 'Norway', 'code': 'NO', 'flag': '🇳🇴'},
    {'name': 'Pakistan', 'code': 'PK', 'flag': '🇵🇰'},
    {'name': 'Peru', 'code': 'PE', 'flag': '🇵🇪'},
    {'name': 'Philippines', 'code': 'PH', 'flag': '🇵🇭'},
    {'name': 'Poland', 'code': 'PL', 'flag': '🇵🇱'},
    {'name': 'Portugal', 'code': 'PT', 'flag': '🇵🇹'},
    {'name': 'Qatar', 'code': 'QA', 'flag': '🇶🇦'},
    {'name': 'Romania', 'code': 'RO', 'flag': '🇷🇴'},
    {'name': 'Russia', 'code': 'RU', 'flag': '🇷🇺'},
    {'name': 'Saudi Arabia', 'code': 'SA', 'flag': '🇸🇦'},
    {'name': 'Serbia', 'code': 'RS', 'flag': '🇷🇸'},
    {'name': 'Singapore', 'code': 'SG', 'flag': '🇸🇬'},
    {'name': 'Slovakia', 'code': 'SK', 'flag': '🇸🇰'},
    {'name': 'Slovenia', 'code': 'SI', 'flag': '🇸🇮'},
    {'name': 'South Africa', 'code': 'ZA', 'flag': '🇿🇦'},
    {'name': 'South Korea', 'code': 'KR', 'flag': '🇰🇷'},
    {'name': 'Spain', 'code': 'ES', 'flag': '🇪🇸'},
    {'name': 'Sri Lanka', 'code': 'LK', 'flag': '🇱🇰'},
    {'name': 'Sweden', 'code': 'SE', 'flag': '🇸🇪'},
    {'name': 'Switzerland', 'code': 'CH', 'flag': '🇨🇭'},
    {'name': 'Taiwan', 'code': 'TW', 'flag': '🇹🇼'},
    {'name': 'Thailand', 'code': 'TH', 'flag': '🇹🇭'},
    {'name': 'Turkey', 'code': 'TR', 'flag': '🇹🇷'},
    {'name': 'Ukraine', 'code': 'UA', 'flag': '🇺🇦'},
    {'name': 'United Arab Emirates', 'code': 'AE', 'flag': '🇦🇪'},
    {'name': 'United Kingdom', 'code': 'GB', 'flag': '🇬🇧'},
    {'name': 'United States', 'code': 'US', 'flag': '🇺🇸'},
    {'name': 'Uruguay', 'code': 'UY', 'flag': '🇺🇾'},
    {'name': 'Venezuela', 'code': 'VE', 'flag': '🇻🇪'},
    {'name': 'Vietnam', 'code': 'VN', 'flag': '🇻🇳'},
    {'name': 'Other', 'code': 'OTHER', 'flag': '🌍'},
  ];

  @override
  void initState() {
    super.initState();
    _filteredCountries = List.from(_countries);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );
    _animationController.forward();
    _detectUserLocation();
  }

  Future<void> _detectUserLocation() async {
    try {
      // Try multiple IP geolocation services for better reliability
      final services = [
        'https://ipapi.co/json/',
        'https://api.ipify.org?format=json', // This only gives IP, need another call
        'http://ip-api.com/json/', // Fallback to HTTP if HTTPS fails
      ];

      for (final serviceUrl in services) {
        try {
          final response = await http
              .get(
                Uri.parse(serviceUrl),
                headers: {'Accept': 'application/json'},
              )
              .timeout(const Duration(seconds: 5));

          if (response.statusCode == 200) {
            final data = json.decode(response.body);
            String? countryCode;

            // Handle different API response formats
            if (serviceUrl.contains('ipapi.co')) {
              countryCode = data['country_code'] as String?;
            } else if (serviceUrl.contains('ip-api.com')) {
              countryCode = data['countryCode'] as String?;
            } else if (serviceUrl.contains('ipify.org')) {
              // ipify only gives IP, skip this for now
              continue;
            }

            if (countryCode != null && mounted) {
              // Find the country in our list
              final country = _countries.firstWhere(
                (c) => c['code'] == countryCode,
                orElse: () => {'name': '', 'code': '', 'flag': ''},
              );

              if (country['name']!.isNotEmpty) {
                setState(() {
                  _selectedCountry = country['name'];
                  _selectedCountryCode = country['code'];
                  _isDetectingLocation = false;
                });
                return; // Success, exit the loop
              }
            }
          }
        } catch (e) {
          // Continue to next service if this one fails
          continue;
        }
      }

      // If all services fail, just continue without pre-selection
      if (mounted) {
        setState(() {
          _isDetectingLocation = false;
          _locationDetectionFailed = true;
        });
      }
    } catch (e) {
      // If detection fails, just continue without pre-selection
      if (mounted) {
        setState(() {
          _isDetectingLocation = false;
          _locationDetectionFailed = true;
        });
      }
    }
  }

  void _filterCountries(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredCountries = List.from(_countries);
      } else {
        _filteredCountries =
            _countries
                .where(
                  (country) => country['name']!.toLowerCase().contains(
                    query.toLowerCase(),
                  ),
                )
                .toList();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(title: const Text('Where are you located?'), elevation: 0),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              colorScheme.tertiary.withValues(alpha: 0.05),
              colorScheme.surface,
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          const SizedBox(height: 32),

                          // Header Card
                          Container(
                            padding: const EdgeInsets.all(24),
                            decoration: BoxDecoration(
                              color: colorScheme.surface,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.05),
                                  blurRadius: 10,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.location_on_outlined,
                                  size: 48,
                                  color: colorScheme.tertiary,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Your Location',
                                  style: theme.textTheme.headlineMedium
                                      ?.copyWith(
                                        color: colorScheme.tertiary,
                                        fontWeight: FontWeight.w600,
                                      ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'This helps us provide you with the right emergency services and support resources for your area.',
                                  style: theme.textTheme.bodyLarge?.copyWith(
                                    color: colorScheme.onSurfaceVariant,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 32),

                          // Country Selection
                          Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: colorScheme.surface,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.05),
                                  blurRadius: 10,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        'Select your country or region:',
                                        style: theme.textTheme.titleMedium
                                            ?.copyWith(
                                              color: colorScheme.onSurface,
                                              fontWeight: FontWeight.w600,
                                            ),
                                      ),
                                    ),
                                    if (_isDetectingLocation)
                                      SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                colorScheme.primary,
                                              ),
                                        ),
                                      ),
                                    if (_locationDetectionFailed)
                                      Tooltip(
                                        message:
                                            'Auto-detection failed. Please select manually.',
                                        child: Icon(
                                          Icons.info_outline,
                                          size: 16,
                                          color: colorScheme.outline,
                                        ),
                                      ),
                                  ],
                                ),
                                const SizedBox(height: 16),
                                // Search field
                                TextField(
                                  controller: _searchController,
                                  onChanged: _filterCountries,
                                  decoration: InputDecoration(
                                    hintText: 'Search countries...',
                                    prefixIcon: Icon(
                                      Icons.search,
                                      color: colorScheme.onSurfaceVariant,
                                    ),
                                    filled: true,
                                    fillColor: colorScheme
                                        .surfaceContainerHighest
                                        .withValues(alpha: 0.3),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide.none,
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide(
                                        color: colorScheme.primary,
                                        width: 2,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 16),
                                SizedBox(
                                  height: 300, // Fixed height for the list
                                  child: ListView.builder(
                                    itemCount: _filteredCountries.length,
                                    itemBuilder: (context, index) {
                                      final country = _filteredCountries[index];
                                      final isSelected =
                                          _selectedCountryCode ==
                                          country['code'];
                                      final hasServices = _supportedCountries
                                          .contains(country['code']);

                                      return Container(
                                        margin: const EdgeInsets.only(
                                          bottom: 8,
                                        ),
                                        child: Material(
                                          color: Colors.transparent,
                                          child: InkWell(
                                            borderRadius: BorderRadius.circular(
                                              12,
                                            ),
                                            onTap: () {
                                              setState(() {
                                                _selectedCountry =
                                                    country['name'];
                                                _selectedCountryCode =
                                                    country['code'];
                                              });
                                            },
                                            child: Container(
                                              padding: const EdgeInsets.all(16),
                                              decoration: BoxDecoration(
                                                color:
                                                    isSelected
                                                        ? colorScheme
                                                            .primaryContainer
                                                        : colorScheme
                                                            .surfaceContainerHighest
                                                            .withValues(
                                                              alpha: 0.3,
                                                            ),
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                                border:
                                                    isSelected
                                                        ? Border.all(
                                                          color:
                                                              colorScheme
                                                                  .primary,
                                                          width: 2,
                                                        )
                                                        : null,
                                              ),
                                              child: Row(
                                                children: [
                                                  Text(
                                                    country['flag']!,
                                                    style: const TextStyle(
                                                      fontSize: 24,
                                                    ),
                                                  ),
                                                  const SizedBox(width: 12),
                                                  Expanded(
                                                    child: Text(
                                                      country['name']!,
                                                      style: theme
                                                          .textTheme
                                                          .bodyLarge
                                                          ?.copyWith(
                                                            color:
                                                                isSelected
                                                                    ? colorScheme
                                                                        .onPrimaryContainer
                                                                    : colorScheme
                                                                        .onSurface,
                                                            fontWeight:
                                                                isSelected
                                                                    ? FontWeight
                                                                        .w600
                                                                    : FontWeight
                                                                        .normal,
                                                          ),
                                                    ),
                                                  ),
                                                  if (hasServices)
                                                    Container(
                                                      padding:
                                                          const EdgeInsets.symmetric(
                                                            horizontal: 8,
                                                            vertical: 4,
                                                          ),
                                                      decoration: BoxDecoration(
                                                        color: Colors.green
                                                            .withValues(
                                                              alpha: 0.1,
                                                            ),
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                              8,
                                                            ),
                                                      ),
                                                      child: Text(
                                                        'Full Support',
                                                        style: theme
                                                            .textTheme
                                                            .bodySmall
                                                            ?.copyWith(
                                                              color:
                                                                  Colors
                                                                      .green
                                                                      .shade700,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                            ),
                                                      ),
                                                    ),
                                                  if (isSelected)
                                                    Icon(
                                                      Icons.check_circle,
                                                      color:
                                                          colorScheme.primary,
                                                      size: 24,
                                                    ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 24),
                        ],
                      ),
                    ),
                  ),
                  // Continue button at bottom
                  Container(
                    padding: const EdgeInsets.all(24.0),
                    child: ElevatedButton(
                      onPressed:
                          _selectedCountryCode == null
                              ? null
                              : () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) => BuildSpaceScreen(
                                          name: widget.name,
                                          age: widget.age,
                                          location: _selectedCountry!,
                                          countryCode: _selectedCountryCode!,
                                        ),
                                  ),
                                );
                              },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: colorScheme.tertiary,
                        foregroundColor: Colors.white,
                        disabledBackgroundColor: colorScheme.outline.withValues(
                          alpha: 0.3,
                        ),
                        elevation: _selectedCountryCode == null ? 0 : 3,
                        shadowColor: colorScheme.tertiary.withValues(
                          alpha: 0.3,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 18),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (_selectedCountryCode != null) ...[
                            const Icon(Icons.arrow_forward_outlined, size: 20),
                            const SizedBox(width: 8),
                          ],
                          Text(
                            _selectedCountryCode == null
                                ? 'Select your location to continue'
                                : 'Continue to Focus Areas',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
